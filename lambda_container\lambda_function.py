import boto3
import fitz  # PyMuPDF
import cv2
import numpy as np
import io
import logging
import json
import os
import tempfile
from datetime import datetime

logger = logging.getLogger()
logger.setLevel(logging.INFO)

s3 = boto3.client('s3')
event_source = None

# Configuration for different environments
CONFIG = {
    "DEV": {
        "base_url": os.environ.get("DEV_BASE_URL"),
        "target_s3_bucket_name": "ibe-dev-attach",
        "sqs_queue_url": os.environ.get("SQS_QUEUE_URL")
    },
    "SQA": {
        "base_url": os.environ.get("SQA_BASE_URL"),
        "target_s3_bucket_name": "ibe-sqa-attach",
        "sqs_queue_url": os.environ.get("SQS_QUEUE_URL")
    },
    "PROD": {
        "base_url": os.environ.get("PROD_BASE_URL"),
        "target_s3_bucket_name": "ibe-prod-attach",
        "sqs_queue_url": os.environ.get("SQS_QUEUE_URL")
    }
}

aws_s3_event_source_name = "aws:s3"

def send_message_to_sqs(sqs_client, sqs_queue_url, message_body):
    """Send a message to SQS queue for error tracking and monitoring."""
    try:        
        response = sqs_client.send_message(
            QueueUrl=sqs_queue_url,
            MessageBody=json.dumps(message_body)
        )
        logger.info(f"Message sent to SQS: {response.get('MessageId')}, MessageBody: {json.dumps(message_body)}")
    except Exception as e:
        logger.error(f"Failed to send message to SQS: {e}")
        raise

def save_page_to_memory(doc, page_number, dpi=200):
    """Convert PDF page to numpy array for analysis"""
    page = doc.load_page(page_number)
    zoom = dpi / 72  # scale = dpi / default dpi
    mat = fitz.Matrix(zoom, zoom)
    pix = page.get_pixmap(matrix=mat)
    
    # Convert to numpy array
    img_data = pix.tobytes("png")
    nparr = np.frombuffer(img_data, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_GRAYSCALE)
    return img

def is_blank_page_advanced(img, threshold=0.98):
    """
    Advanced blank page detection using multiple methods
    Similar accuracy to the original function but using OpenCV
    """
    try:
        # Method 1: White pixel ratio (original approach)
        _, binary = cv2.threshold(img, 200, 255, cv2.THRESH_BINARY)
        white_pixel_ratio = np.sum(binary == 255) / binary.size
        
        # Method 2: Edge detection
        edges = cv2.Canny(img, 50, 150)
        edge_pixel_ratio = np.sum(edges > 0) / edges.size
        
        # Method 3: Standard deviation (content variation)
        std_dev = np.std(img)
        
        # Method 4: Histogram analysis
        hist = cv2.calcHist([img], [0], None, [256], [0, 256])
        # Normalize histogram
        hist = hist.flatten() / img.size
        
        # Check concentration in white/black ranges
        white_concentration = np.sum(hist[240:256])  # Very white pixels
        black_concentration = np.sum(hist[0:16])     # Very black pixels
        uniform_concentration = white_concentration + black_concentration
        
        # Combined decision logic for maximum accuracy
        is_blank = (
            white_pixel_ratio >= threshold and          # High white pixel ratio
            edge_pixel_ratio < 0.01 and                # Very few edges
            std_dev < 15 and                           # Low variation
            uniform_concentration > 0.95               # Mostly uniform colors
        )
        
        logger.info(f"Page analysis - White ratio: {white_pixel_ratio:.4f}, "
                   f"Edge ratio: {edge_pixel_ratio:.4f}, Std dev: {std_dev:.2f}, "
                   f"Uniform: {uniform_concentration:.3f}, Is blank: {is_blank}")
        
        return is_blank
        
    except Exception as e:
        logger.error(f"Error in blank page detection: {str(e)}")
        return False


def lambda_handler(event, context):
    """
    PyMuPDF-based PDF splitter integrated with iBuyEfficient workflow
    
    Expected event structure:
    {
        "bucket": "source-bucket-name",
        "key": "path/to/original-file.pdf",
        "company_id": "12345",
        "environment": "PROD",
        "original_attachment_id": "67890",
        "uses_ocr": true,
        "original_file_name": "invoice_batch.pdf"
    }
    """
    try:
        logger.info(f"Received PDF splitting request: {json.dumps(event)}")
        
        # Extract parameters from event
        bucket = event.get('bucket')
        key = event.get('key')
        company_id = event.get('company_id')
        environment = event.get('environment')
        original_attachment_id = event.get('original_attachment_id')
        uses_ocr = event.get('uses_ocr', False)
        original_file_name = event.get('original_file_name', 'unknown.pdf')
        
        # Validate required parameters
        if not all([bucket, key, company_id, environment, original_attachment_id]):
            raise ValueError("Missing required parameters: bucket, key, company_id, environment, original_attachment_id")
        
        logger.info(f"Processing PDF: s3://{bucket}/{key} using PyMuPDF")
        
        # Generate unique timestamp for temp folder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        
        # Initialize clients
        s3 = boto3.client('s3')
        sqs_client = boto3.client("sqs")
        global event_source
        event_source = aws_s3_event_source_name
        
        # Download PDF from S3
        response = s3.get_object(Bucket=bucket, Key=key)
        pdf_data = response['Body'].read()
        
        logger.info(f"Downloaded PDF, size: {len(pdf_data)} bytes")
        
        # Open PDF with PyMuPDF
        doc = fitz.open(stream=pdf_data, filetype="pdf")
        total_pages = len(doc)
        
        logger.info(f"Opened PDF with {total_pages} pages")
        
        # Analyze each page for blank detection
        documents = []
        current_pages = []
        blank_pages_found = []
        
        for page_num in range(total_pages):
            logger.info(f"Analyzing page {page_num + 1}/{total_pages}...")
            
            # Convert page to image for analysis
            img = save_page_to_memory(doc, page_num, dpi=200)
            
            # Detect blank page with high accuracy
            is_blank = is_blank_page_advanced(img, threshold=0.98)
            
            if is_blank:
                logger.info(f"✓ Confirmed blank page at position {page_num + 1}")
                blank_pages_found.append(page_num + 1)
                
                # Save current document if it has pages
                if current_pages:
                    documents.append(current_pages.copy())
                    current_pages = []
            else:
                logger.info(f"✓ Content page at position {page_num + 1}")
                current_pages.append(page_num)
        
        # Add the last document if it has pages
        if current_pages:
            documents.append(current_pages)
        
        logger.info(f"Split analysis complete: {len(documents)} documents created")
        logger.info(f"Blank pages found at positions: {blank_pages_found}")
        
        # Handle case where no splits are created or only one document
        if not documents:
            logger.info("No content pages found - entire PDF may be blank")
            doc.close()
            return {
                'statusCode': 200,
                'body': {
                    'message': 'No content pages found - entire PDF appears to be blank',
                    'split_count': 0,
                    'original_attachment_id': original_attachment_id
                }
            }
        
        if len(documents) == 1:
            logger.info("No splitting required - PDF contains only one document")
            doc.close()
            return {
                'statusCode': 200,
                'body': {
                    'message': 'No splitting required - PDF contains only one document',
                    'split_count': 1,
                    'original_attachment_id': original_attachment_id
                }
            }
        
        # Create split files and upload to S3 for main processor to handle workflow
        # Note: Files are temporarily created in this Lambda's /tmp/, uploaded to S3 target bucket,
        # then downloaded by main processor to its /tmp/ for processing, and finally archived
        # File naming: {original_name}_1.pdf, {original_name}_2.pdf, etc.
        split_file_paths = []
        
        for idx, page_list in enumerate(documents, start=1):
            logger.info(f"Creating document part {idx} with pages {[p+1 for p in page_list]}...")
            
            try:
                # Create new PDF with selected pages
                new_doc = fitz.open()
                for page_num in page_list:
                    new_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
                
                # Create split file and upload to S3 temp location
                # Extract base name without extension from original file
                base_name = os.path.splitext(original_file_name)[0]
                split_file_name = f"{base_name}_{idx:03d}.pdf"
                local_split_file = f"/tmp/{split_file_name}"
                new_doc.save(local_split_file)
                new_doc.close()
                
                # Upload split file to target bucket split-files folder for main processor to access
                target_bucket = CONFIG[environment]["target_s3_bucket_name"]
                temp_s3_key = f"split-files/{split_file_name}"
                
                try:
                    with open(local_split_file, 'rb') as f:
                        s3.put_object(
                            Bucket=target_bucket,
                            Key=temp_s3_key,
                            Body=f.read(),
                            ContentType='application/pdf'
                        )
                    
                    logger.info(f"✓ Uploaded split file to target bucket: s3://{target_bucket}/{temp_s3_key}")
                    
                    # Clean up local file
                    os.remove(local_split_file)
                    
                    # Add S3 location to split files list for main processor to download
                    split_file_paths.append({
                        "file_name": split_file_name,
                        "s3_bucket": target_bucket,
                        "s3_key": temp_s3_key,
                        "part_number": idx,
                        "page_count": len(page_list)
                    })
                    
                    logger.info(f"✓ Created split file {idx}: {split_file_name} ({len(page_list)} pages)")
                    
                except Exception as upload_error:
                    logger.error(f"Failed to upload split file {idx} to S3: {str(upload_error)}")
                    # Clean up local file even if upload failed
                    try:
                        os.remove(local_split_file)
                    except:
                        pass
                    # Continue with other files
                    continue
                
            except Exception as e:
                logger.error(f"Error creating split file {idx}: {str(e)}")
                # Continue with other files even if one fails
                continue
        
        doc.close()
        
        # Prepare success response with split file paths for main processor
        result = {
            'statusCode': 200,
            'body': {
                'message': f'Successfully created {len(split_file_paths)} split files',
                'split_count': len(split_file_paths),
                'original_attachment_id': original_attachment_id,
                'split_file_paths': split_file_paths,
                'blank_pages_found': blank_pages_found
            }
        }
        
        logger.info("PDF splitting completed successfully!")
        return result
        
    except Exception as e:
        logger.error(f"Error in PDF splitting: {str(e)}")
        
        # Send error to SQS for monitoring
        try:
            if 'sqs_client' in locals() and 'environment' in locals():
                sqs_message = {
                    "error": str(e),
                    "operation": "lambda_handler",
                    "event": event,
                    "timestamp": datetime.now().isoformat()
                }
                sqs_queue_url = CONFIG[environment]["sqs_queue_url"]
                send_message_to_sqs(sqs_client, sqs_queue_url, sqs_message)
        except:
            pass  # Don't let SQS errors mask the original error
        
        # Clean up resources
        if 'doc' in locals():
            doc.close()
            
        return {
            'statusCode': 500,
            'body': {
                'error': str(e),
                'message': 'PDF splitting failed'
            }
        }

# # For local testing
# if __name__ == "__main__":
#     test_event = {
#         'bucket': 'ibe-gd-preprod-attach',
#         'key': 'DEV/Email/test_company/test-document.pdf',
#         'company_id': 'TEST_COMPANY',
#         'environment': 'DEV',
#         'original_attachment_id': 'test_12345',
#         'uses_ocr': True,
#         'original_file_name': 'test-document.pdf'
#     }
    
#     result = lambda_handler(test_event, None)
#     print(json.dumps(result, indent=2)) 